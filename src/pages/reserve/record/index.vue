<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '预约记录',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'always',
    },
    'app-plus': {
      titleNView: {
        type: 'transparent',
        titleText: '预约记录',
      },
    },
  },
}
</route>

<template>
  <view class="h-100vh flex flex-col">
    <TransparentTitle title="预约记录" />
    <view class="common-header-bg"></view>
    <view class="z-3 mt-24rpx flex-1 overflow-hidden flex flex-col">
      <ATabs :list="tabList" :current="tabCurrent" type="capsule" @click="tabCurrentChange" />
      <view class="flex-1 mt-50" v-if="!list.length">
        <AEmpty className="mt-50" v-if="isQuerying" type="loading" />
        <AEmpty
          v-else
          :type="error ? 'net' : 'content'"
          :button="true"
          @buttonClick="refreshList"
        />
      </view>
      <scroll-view
        v-else
        :scroll-y="true"
        :enhanced="true"
        :show-scrollbar="false"
        class="w-full mt-6 px-6 pb-6 box-border overflow-y-auto flex-1"
        @scrolltolower="getNextPage"
        :lower-threshold="150"
      >
        <view class="flex flex-col">
          <view
            v-for="(item, index) in list"
            :key="index"
            :class="index > 0 ? 'mt-24rpx' : ''"
            @click="goToDetail(item)"
          >
            <view class="bg-white rounded-12rpx p-6">
              <view class="flex justify-between items-center">
                <text class="text-primary-28 font-medium text-text-primary truncate max-w-70%">
                  {{ item.parkingName || '停车场' }}
                </text>
                <text :class="['text-secondary-26', getStatusClass(item.status)]">
                  {{ getStatusText(item.status) }}
                </text>
              </view>
              <view class="h-1rpx w-full bg-divider-color my-24rpx"></view>
              <view class="flex flex-col gap-8rpx">
                <view class="flex items-center">
                  <text class="text-secondary-26 text-#999">车牌号：</text>
                  <text class="text-secondary-26 text-#666">
                    {{ item.plateNo || '--'
                    }}{{
                      item.plateColor
                        ? ` (${item.plateColor === 'BLUE' ? '蓝' : item.plateColor === 'YELLOW' ? '黄' : item.plateColor === 'GREEN' ? '绿' : '其他'})`
                        : ''
                    }}
                  </text>
                </view>
                <view class="flex items-center">
                  <text class="text-secondary-26 text-#999">预约入场时间：</text>
                  <text class="text-secondary-26 text-#666">
                    {{ item.reservationTime || '--' }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import ATabs from '@/components/ATabs/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { usePageRequest } from '@/hooks/usePageRequest';
import { ParkingReserveService, type UserReservationRecordVO } from '@/parkService';

// 标签页数据
const tabList = ref([
  { label: '全部', value: 'ALL' },
  { label: '已预约', value: 'RESERVED' },
  { label: '已履约', value: 'FULFILLED' },
  { label: '已爽约', value: 'ARRIVED' },
  { label: '已取消', value: 'CANCELED' },
]);

const tabCurrent = ref(0);

// 状态映射：页面状态 -> 接口状态
const statusMap = {
  ALL: undefined, // 全部不传状态参数
  RESERVED: 1, // 已预约
  FULFILLED: 4, // 已履约
  ARRIVED: 3, // 已爽约
  CANCELED: 2, // 已取消
} as const;

// 状态显示映射：接口状态 -> 显示文本
const statusDisplayMap = {
  0: '初始',
  1: '已预约',
  2: '已取消',
  3: '已爽约',
  4: '已履约',
} as const;

// 请求API配置
const requestApi = computed(() => {
  const currentTabValue = tabList.value[tabCurrent.value]?.value as keyof typeof statusMap;
  const status = statusMap[currentTabValue];

  return {
    request: ParkingReserveService.postReservationQueryRecordPage,
    businessParams: status !== undefined ? { status } : {},
  };
});

// 页面初始化标记
const isPageInitialized = ref(false);

// 使用分页请求hook
const { pageNum, hasMore, list, isQuerying, error, getData, refreshList } = usePageRequest(
  requestApi,
  true,
  true,
  10,
);

// 页面显示时刷新数据
onShow(() => {
  if (isPageInitialized.value) {
    // 非首次进入页面时刷新数据
    refreshList();
  } else {
    // 首次进入页面，标记为已初始化
    isPageInitialized.value = true;
  }
});

// 切换标签页
const tabCurrentChange = (index: number) => {
  tabCurrent.value = index;
  // 切换标签页时会自动触发 requestApi 的变化，从而重新请求数据
};

// 获取下一页
const getNextPage = () => {
  if (hasMore.value && !isQuerying.value) {
    getData();
  }
};

// 跳转到详情页
const goToDetail = (item: UserReservationRecordVO) => {
  uni.navigateTo({
    url: `/pages/reserve/recordDetail/index?id=${item.id}`,
  });
};

// 获取状态显示文本
const getStatusText = (status?: number) => {
  if (status === undefined || status === null) return '未知';
  return statusDisplayMap[status as keyof typeof statusDisplayMap] || '未知';
};

// 获取状态样式类
const getStatusClass = (status?: number) => {
  return status === 1 ? 'text-#56be66' : 'text-#ccc';
};
</script>

<style scoped lang="scss">
.common-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 360rpx;
  background: linear-gradient(180deg, #56be66 0%, rgb(86 190 102 / 0%) 100%);
}
</style>
