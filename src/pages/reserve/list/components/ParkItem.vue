<template>
  <view :class="['common-card', type === 'list' ? 'mb-6' : 'mb-3']">
    <view class="flex-between items-center">
      <view class="text-primary-32 text-text-primary truncate">
        {{ name }}
      </view>
      <view v-if="distance >= 0" class="flex items-center" @click.stop="goNav">
        <OSSImage :width="36" :height="36" src="/images/common/businessCard/card-distance.png" />
        <view class="text-secondary-24 text-text-secondary ml-2">{{ distance }}km</view>
      </view>
    </view>
    <view class="address flex w-full mt-3 items-center">
      <OSSImage
        class-name="shrink-0"
        :width="24"
        :height="24"
        src="/images/common/businessCard/card-loc.png"
      />
      <view v-if="address" class="text-secondary-24 flex-1 text-text-sub truncate ml-1">
        {{ address }}
      </view>
    </view>
    <slot />
    <view class="tips rounded-2 inline-flex items-center bg-page-background mt-6 w-auto pr-2">
      <OSSImage
        class-name="shrink-0"
        :width="40"
        :height="40"
        src="/images/common/businessCard/park-icon.png"
      />
      <view class="text-auxiliary ml-2 truncate flex">
        <view class="text-text-primary font-500 shrink-0">空{{ remainNum || 0 }}</view>
        <view class="text-text-sub truncate">/{{ plateNum || 0 }}</view>
      </view>
    </view>

    <view class="flex-between items-center w-full mt-3">
      <BProgressInfo
        class="w-full"
        :className="'w-full'"
        progressName="可预约泊位"
        :progressStatus="getProgressStatus(reserveConfig?.spaceStatus || '')"
        :leftNum="reserveConfig?.curReservableSpaces || 0"
        :totalNum="reserveConfig?.maxReservableSpaces || 0"
      />
    </view>

    <view class="flex text-[22rpx] leading-[32rpx] items-baseline mt-3">
      <view class="mr-4">开放预约时间</view>
      <view class="text-text-secondary">
        {{ reserveConfig?.startTime }}
        <span v-if="reserveConfig?.startTime && reserveConfig?.endTime">-</span>
        {{ reserveConfig?.endTime }}
      </view>
    </view>

    <view class="flex text-[22rpx] leading-[32rpx] items-baseline mt-3">
      <view class="mr-4">开放预约日期</view>
      <view class="text-text-secondary">
        {{ formatAvailableWeekdays(reserveConfig?.availableWeekdays) }}
      </view>
    </view>
    <template v-if="type === 'list'">
      <view class="h-1rpx w-full bg-#eee my-6" />
      <view
        class="w-136rpx h-56rpx bg-#56be66 ml-a rounded-[29rpx] text-white-color text-[26rpx] leading-[56rpx] text-center"
        @click="onReserveClick"
      >
        我要预约
      </view>
    </template>

    <template v-if="type === 'detail'">
      <!-- 服务项 - 预约车停车指引 -->
      <view class="flex justify-center w-full mt-6 mb-3" @click="handleGuidance">
        <view class="flex items-center justify-center">
          <view class="text-28rpx leading-40rpx text-#56be66">预约车停车指引</view>
          <view class="ml-2 relative">
            <OSSImg src="/images/reserve/arrow.png" width="24" height="24" />
          </view>
        </view>
      </view>
    </template>
  </view>
</template>
<script setup lang="ts">
import OSSImage from '@/components/OSSImg/index.vue';
import { getProgressStatus } from '@/utils/stationStatus';
import type { ReservationParkingConfigModel } from '@/parkService/models/ReservationParkingConfigModel';

declare const uni: {
  openLocation: (options: {
    latitude: number;
    longitude: number;
    name: string;
    address: string;
  }) => void;
  navigateTo: (options: { url: string }) => void;
  showToast: (options: { title: string; icon?: string }) => void;
};

const props = withDefaults(
  defineProps<{
    type: 'list' | 'detail';
    name: string;
    address: string;
    distance: number;
    tipInfo: string;
    remark: string;
    latitude: number | string;
    longitude: number | string;
    parkId: string | number;
    openTime?: string;
    openDate?: string;
    // 添加与BaseParkDetailVO匹配的字段
    remainNum?: number;
    plateNum?: number;
    spaceStatus?: string;
    reserveConfig?: ReservationParkingConfigModel;
  }>(),
  {
    type: 'list',
    name: '',
    address: '',
    distance: 0,
    tipInfo: '',
    remark: '',
    latitude: 0,
    longitude: 0,
    parkId: '',
    openTime: '',
    openDate: '',
    remainNum: 0,
    plateNum: 0,
    spaceStatus: '',
  },
);

// 格式化可预约星期几
const formatAvailableWeekdays = (weekdays?: Array<1 | 2 | 3 | 4 | 5 | 6 | 7>) => {
  if (!weekdays || weekdays.length === 0) {
    return '--';
  }

  const weekdayMap: Record<number, string> = {
    1: '星期一',
    2: '星期二',
    3: '星期三',
    4: '星期四',
    5: '星期五',
    6: '星期六',
    7: '星期日',
  };

  const sortedWeekdays = [...weekdays].sort((a, b) => a - b);

  // 其他情况，显示为逗号分隔的列表
  return sortedWeekdays.map((day) => weekdayMap[day]).join('、');
};

const onReserveClick = () => {
  uni.navigateTo({
    url: `/pages/reserve/detail/index?parkId=${props.parkId}`,
  });
};

// 处理停车指引
const handleGuidance = () => {
  // 停车指引功能实现
  uni.showToast({
    title: '正在开发中',
    icon: 'none',
  });
};

const goNav = () => {
  if (props.latitude && props.longitude) {
    uni.openLocation({
      latitude: Number(props.latitude),
      longitude: Number(props.longitude),
      name: props.name,
      address: props.address,
    });
  }
};
</script>
<style lang="scss" scoped></style>
