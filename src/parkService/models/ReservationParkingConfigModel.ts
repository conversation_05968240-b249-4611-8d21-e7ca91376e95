/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
export type ReservationParkingConfigModel = {
  /**
   * 场站ID
   */
  parkingId: string;
  /**
   * 车场停车预约开关：0-关闭，1-开启
   */
  reservationEnabled: ReservationParkingConfigModel.reservationEnabled;
  /**
   * 每周可预约日
   * [1,1,1,1,1,1,1]
   */
  availableWeekdays: Array<1 | 2 | 3 | 4 | 5 | 6 | 7>;
  /**
   * 每日可预约开始时间
   * 08:00
   */
  startTime: string;
  /**
   * 每日可预约结束时间
   * 12:00
   */
  endTime: string;
  /**
   * 最大可预约车位数
   */
  maxReservableSpaces: number;
  /**
   * 当前可预约车位数
   */
  curReservableSpaces?: number;
  /**
   * 预约车辆停车指引
   */
  parkingGuidance?: string;
  /**
   * 预约车位状态：00爆满 01拥挤 02空闲 03已满
   */
  spaceStatus?: string;
  /**
   * 当前日是否可以预约
   */
  reservableDay?: boolean;
};
export namespace ReservationParkingConfigModel {
  /**
   * 车场停车预约开关：0-关闭，1-开启
   */
  export enum reservationEnabled {
    '_0' = 0,
    '_1' = 1,
  }
}
